import {
  HttpInterceptorFn,
  HttpErrorResponse
} from '@angular/common/http';
import { inject } from '@angular/core';
import { ErrorModalService } from '@core/services/error-modal.service';
import { throwError, from } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
    const errorModalService = inject(ErrorModalService);

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      console.log('error', error);

      if (error.error instanceof Blob && error.error.type === 'application/json') {
        // 💡 Convert Blob to JSON
        return from(error.error.text()).pipe(
          switchMap((text: string) => {
            console.log('text', text);
            try {
              const parsedError = JSON.parse(text);
              const message =
                parsedError.message ||
                parsedError.error_description ||
                parsedError.title ||
                parsedError.detail ||
                'حدث خطأ غير متوقع';

              const enrichedError = {
                ...error,
                parsedMessage: message,
                responseBody: parsedError
              };
              errorModalService.showError(message);

              return throwError(() => enrichedError);
            } catch (parseError) {
              console.error('Failed to parse error response as JSON:', parseError);
              const message = 'حدث خطأ غير متوقع';
              const enrichedError = {
                ...error,
                parsedMessage: message,
                responseBody: text
              };
              errorModalService.showError(message);
              return throwError(() => enrichedError);
            }
          })
        );
      }

      // ✅ Normal JSON or string error
      let message = 'حدث خطأ غير متوقع';
      const responseBody = error.error;

      if (responseBody) {
        if (typeof responseBody === 'string') {
          message = responseBody;
        } else if (typeof responseBody === 'object') {
          message =
            responseBody.message ||
            responseBody.error_description ||
            responseBody.title ||
            responseBody.detail ||
            message;
        }
      }

      const enrichedError = {
        ...error,
        parsedMessage: message,
        responseBody
      };
      errorModalService.showError(message);

      return throwError(() => enrichedError);
    })
  );
};
