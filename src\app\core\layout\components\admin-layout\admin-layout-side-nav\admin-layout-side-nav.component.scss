@import "../../../../../../assets/scss/_variables.scss";

.sidenav {
  width: 18rem;
  height: 100vh;
  background-color: $navy-blue;
  background-image: url("/assets/images/Subtract.png");
  background-repeat: no-repeat;
  background-position: bottom;
  background-size: contain;
  display: flex;
  flex-direction: column;
  padding: 1rem 1rem;
  color: white;
  z-index: 1030; // Higher than Bootstrap's navbar
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
  opacity: 1;
  visibility: visible;

  // Remove default border radius (we’ll handle it based on direction)
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  // Direction-based border radius
  html[dir="rtl"] & {
    border-top-left-radius: 1rem;
  }

  html[dir="ltr"] & {
    border-top-right-radius: 1rem;
  }

  &.d-none {
    transform: translateX(100%);
    opacity: 0;
    visibility: hidden;
  }

  // Mobile responsive behavior (768px and below)
  @media (max-width: 768px) {
    position: fixed;
    top: 0;
    z-index: 1050; // Higher z-index for mobile overlay
    width: 280px; // Slightly smaller on mobile
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);

    // RTL (Arabic) - slide from right
    &:lang(ar) {
      // right: 0;
      border-top-left-radius: 16px;
      right: -100%;

      &.mobile-open {
        right: 0px;// Slide in from right
      }
    }

    // LTR (English) - slide from left
    html[dir="ltr"] & {
      left: 0;
      border-top-right-radius: 16px;
      transform: translateX(-100%); // Hidden by default

      &.mobile-open {
        transform: translateX(50); // Slide in from left
      }
    }

    &.d-none {
      // Override d-none behavior on mobile
      html[dir="rtl"] & {
        transform: translateX(100%);
      }
      html[dir="ltr"] & {
        transform: translateX(-100%);
      }
      opacity: 0;
      visibility: hidden;
    }
  }
}

.wight-hr {
  color: $grey;
  width: 200px;
  height: 2px;
  margin: 8px auto;
}
.logo-container {
  padding: 1rem;
  text-align: center;
  .logo {
    max-width: 180px;
    height: auto;
  }
}

.nav-links {
  list-style: none;
  padding: 1rem 0;
  font-size: 20px;
  margin: 0;
  flex-grow: 1;
}

.nav-item {
  margin-bottom: 0.5rem;
  font-size: 20px;

  svg {
    margin-left: 0.5rem;
    flex-shrink: 0;
  }
  &.active {
    .nav-link {
      background-color: $white;
      border-radius: 8px;
      font-weight: 500;
      color: $navy-blue !important;

      svg path {
        fill: currentColor;
      }
      a {
        color: $navy-blue !important;
      }
    }
  }
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  border-radius: 8px;
  svg {
    margin: 0 0.5rem;
    flex-shrink: 0;
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: #fff;

    svg path {
      fill: currentColor;
    }
  }

  i {
    margin-left: 1rem;
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    transition: color 0.2s ease;
  }

  .badge {
    position: absolute;
    left: 1rem;
    background-color: #e53935;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 999px;
    font-size: 0.75rem;
    font-weight: 500;
    min-width: 24px;
    text-align: center;
  }
}

.logout-container {
  margin-top: auto;
  .nav-link {
    color: $white;

    &:hover {
      background-color: $white;
      color: $navy-blue;
    }
  }
}

// Mobile overlay/backdrop
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040; // Below sidebar but above content
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.show {
    opacity: 1;
    visibility: visible;
  }

  // Only show on mobile
  @media (min-width: 769px) {
    display: none !important;
  }
}

// Close button for mobile sidebar
.mobile-close-btn {
  position: absolute;
  top: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  // Position based on direction
  html[dir="rtl"] & {
    left: 1rem;
  }

  html[dir="ltr"] & {
    right: 1rem;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }

  // Only show on mobile
  @media (min-width: 769px) {
    display: none !important;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}
