<div class="fund-card-info"
     [class.disabled]="disabled"
     >
  <div class="card-header">
    <div class="notification-container"  (click)="!disabled && filterNotificationHandle()">
      <img src="assets/images/notification.png" />
      <span class="notification-badge" *ngIf="notificationCount > 0">
        {{ notificationCount }}
      </span>
    </div>

    <div class="d-flex justify-content-center align-items-center">
      <img [src]="icon" />
      <h3 class="title mx-2">{{ title }}</h3>
    </div>
  </div>

  <div class="card-content">
    <img src="assets/images/arrow-goto.png" class="rotate-icon" (click)="!disabled && navigate()"/>
    <div class="count m-lg-auto">{{ fundCount }}</div>
  </div>
</div>
