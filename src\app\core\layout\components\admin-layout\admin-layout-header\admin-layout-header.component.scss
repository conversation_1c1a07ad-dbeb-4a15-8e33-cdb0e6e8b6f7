@import "../../../../../../assets/scss/variables";
.form-control:focus{
  box-shadow: none !important;
}

.header-logo {
  img {
    height: 32px;
    width: auto;
  }
}

.header {
  color: $navy-blue;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}
.header-padding {
  padding: 1.75rem;
}

// Mobile styles
@media (max-width: 991.98px) {
  .search-section {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .header {
    font-size: 20px; // Smaller font size on mobile
  }

  .header-logo img {
    height: 28px; // Smaller logo on mobile
  }

  .search-container .search-input {
    height: 44px;
    font-size: 14px;
  }

  .search-container .search-btn {
    width: 28px;
    height: 28px;
    right: 10px;

    svg {
      width: 16px;
      height: 15px;
    }
  }

  // Adjust user profile section for mobile
  .user-profile-section {
    padding: 0px !important;

    span {
      font-size: 12px;
    }
  }

  // Language button adjustments
  .lang-btn {
    padding: 6px 10px;
    font-size: 12px;
    height: 28px;
  }
}

// Notification badge styles are defined later in the file

// Search Section Styles
.search-section {
  width: 263px;
  position: relative;
}

.search-container {
  position: relative;
  display: flex;
  width: 263px;
  padding: 0px 12px;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  border: 2px solid #D1D1D1;
  border-radius: 12px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 10px;

  .search-input {
    flex: 1;
    height: 48px;
    padding: 0;
    border: none;
    background: transparent;
    font-size: 16px;
    color: #333;
    &::placeholder {
      color: #999;
      font-weight: 400;
    }

    &:focus {
      outline: none;
    }
  }

  .search-btn {
    background: none;
    border: none;
    color: #4f4f4f;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    flex-shrink: 0;

    svg {
      width: 19px;
      height: 18px;
    }

    &:hover {
      background: rgba(0, 32, 90, 0.1);

      svg path {
        fill: $navy-blue;
      }
    }
  }
}

.create-fund-btn {
  height: 40px;
  padding: 0 16px;
  background-color: $navy-blue;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  white-space: nowrap;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }

  i {
    font-size: 12px;
  }

  &:hover {
    background-color: darken($navy-blue, 5%);
  }
}
.notification-container{
  min-width: 30px;
  .notification-badge {
    position: absolute;
    right: 9.25px;
    top: 3px;
    background-color: $notification-badg;
    border-radius: 50%;
    display: flex;
    padding: 2px 5px;
    min-width: 16px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
  }
}

.user-avatar {
  width: 32px;
  height: 32px;
  object-fit: cover;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.lang-btn {
  display: flex;
  align-items: self-start;
  gap: 8px;
  font-size: 20px;
  border-color: #00205a;
  padding: 3px 12px;
  border: 1px solid;
  height: 32px;

  span {
    line-height: 1rem;
  }
}

.menu-toggle {
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: white;

  &:hover {
    background-color: #f8f9fa;
  }

  img {
    width: 24px;
    height: 24px;
  }
}

.user-profile-section {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 32, 90, 0.05);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  img {
    transition: all 0.3s ease;
  }

  &:hover img {
    box-shadow: 0 2px 8px rgba(0, 32, 90, 0.2);
  }
  span{
    text-wrap-mode: nowrap;
  }
}

// Language Dropdown Styles
.lang-btn {
  background: transparent;
  border: 1px solid #E0E0E0;
  color: $navy-blue;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
    border-color: $navy-blue;
  }

  &:focus {
    box-shadow: none;
    border-color: $navy-blue;
  }

  .language-flag {
    width: 16px;
    height: 16px;
    object-fit: cover;
    border-radius: 2px;
  }

  .language-text {
    font-size: 14px;
    font-weight: 500;
  }
}

.dropdown-menu {
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 8px 0;
  min-width: 140px;

  .dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    color: $navy-blue;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      color: $navy-blue;
    }

    &.active {
      background-color: $navy-blue;
      color: white;

      .language-flag svg path {
        opacity: 0.9;
      }
    }

    .language-flag {
      width: 16px;
      height: 16px;
      object-fit: cover;
      border-radius: 2px;
    }
  }
}

.menu-btn{
  border: none;
  background-color: transparent;
  padding: 0px;
}

// Notification Dropdown Styles
.notification-wrapper {
  .notification-container {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .notification-badge {
    position: absolute;
    top: 3px;
    background-color: $notification-badg;
    border-radius: 50%;
    display: flex;
    padding: 2px 5px;
    min-width: 16px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;

    // RTL - Arabic (badge on right side of bell icon)
    &:lang(ar) {
      right: 9.25px;
      left: auto;
    }

    // LTR - English (badge on left side of bell icon)
    &:lang(en) {
      left: 9.25px;
      right: auto;
    }
  }
}

// Notification Dropdown Styles
.notification-dropdown {
  position: absolute;
  top: calc(100% + 15px);
  width: 380px;
  max-height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 32, 90, 0.15);
  border: 1px solid rgba(0, 32, 90, 0.1);
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // RTL Support - Arabic (dropdown positioned from left)
  &:lang(ar) {
    left: 0;
    right: auto;
  }

  // LTR Support - English (dropdown positioned from right)
  &:lang(en) {
    right: 0;
    left: auto;
  }

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  // Arrow pointing up to the notification icon
  &::before {
    content: '';
    position: absolute;
    top: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
    filter: drop-shadow(0 -2px 4px rgba(0, 32, 90, 0.1));
  }

  // RTL Support - Arabic (arrow on left side)
  &:lang(ar)::before {
    left: 20px;
    right: auto;
  }

  // LTR Support - English (arrow on right side)
  &:lang(en)::before {
    right: 20px;
    left: auto;
  }

  @media (max-width: 768px) {
    width: 320px;
    max-height: 400px;

    // RTL - Arabic mobile positioning
    html[dir="rtl"] & {
      left: -50px;
      right: auto;
    }

    // LTR - English mobile positioning
    html[dir="ltr"] & {
      right: -50px;
      left: auto;
    }
  }

  @media (max-width: 480px) {
    width: 280px;

    // RTL - Arabic small mobile positioning
    html[dir="rtl"] & {
      left: -80px;
      right: auto;
    }

    // LTR - English small mobile positioning
    html[dir="ltr"] & {
      right: -80px;
      left: auto;
    }
  }
}

// Notification Dropdown Header
.notification-dropdown-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid rgba(0, 32, 90, 0.1);

  .notification-title {
    font-size: 16px;
    font-weight: 600;
    color: $navy-blue;
  }

  .mark-all-read-btn {
    color: $light-dark;
    font-size: 12px;
    font-weight: 400;
    text-decoration: underline;
    border: none;
    background: none;
    padding: 0;
    cursor: pointer;
  }
  .mark-all-read-btn:disabled {
    cursor: not-allowed;
  }
}

// Notification List Container
.notification-list-container {
  max-height: 350px;
  overflow: hidden;
}

// Virtual Scroll Viewport
.notification-virtual-scroll {
  height: 250px;
  width: 100%;
}

// Loading, Error, and Empty States
.notification-loading,
.notification-error,
.notification-empty {
  padding: 20px;
  text-align: center;
}

// Loading more indicator for infinite scroll
.notification-loading-more {
  padding: 10px 20px;
  text-align: center;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }

  span {
    font-size: 0.875rem;
    color: #6c757d;
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Individual Notification Item
.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 10px;
  border-bottom: 1px solid rgba(0, 32, 90, 0.05);
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-height: 80px;

  &:hover {
    background-color: rgba(0, 32, 90, 0.02);
  }

  &.unread {
    background-color: $card-background;
    // border-inline-start: 3px solid $navy-blue
  }

  &:last-child {
    border-bottom: none;
  }

  .notification-icon {
    flex-shrink: 0;
    margin-inline-end: 12px;

    html[dir="rtl"] & {
      margin-right: 0;
      margin-left: 12px;
    }

    img {
      border-radius: 50%;
    }
  }

  .notification-content {
    flex: 1;
    min-width: 0;

    .notification-item-title {
      font-size: 14px;
      font-weight: 400;
      color: $navy-blue;
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .notification-item-body {
      font-size: 13px;
      color: #666;
      margin: 0 0 6px 0;
      line-height: 1.4;
    }

    .notification-time {
      font-size: 12px;
      color: #999;
      font-weight: 400;
      display: block;
      text-align: start;
      direction: ltr;
    }
  }

  .notification-status {
    flex-shrink: 0;
    margin-left: 8px;
    margin-top: 6px;

    html[dir="rtl"] & {
      margin-left: 0;
      margin-right: 8px;
    }

    .unread-indicator {
      width: 8px;
      height: 8px;
      background-color: $navy-blue;
      border-radius: 50%;
      display: block;
    }
  }
}

// Notification Dropdown Footer
.notification-dropdown-footer {
  padding: 12px 20px;
  border-top: 1px solid rgba(0, 32, 90, 0.1);
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;

  .view-all-btn {
    color: $primary-color1;
    font-size: 14px;
    font-weight: 500;
    border: none;
    background: none;
    padding: 8px 0;

    &:hover {
      color: darken($primary-color1, 10%);
      text-decoration: underline !important;
    }
  }
}

::ng-deep{
  .cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{
    min-width: unset !important;
    width: 100%
  }
}
