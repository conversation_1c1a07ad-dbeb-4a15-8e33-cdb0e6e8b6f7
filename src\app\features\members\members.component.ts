import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { AddMemberComponent, AddMemberDialogData } from './add-member/add-member.component';
import { MemberCardComponent } from './member-card/member-card.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { TokenService, userRole } from '../auth/services/token.service';
import {
  BoardMembersServiceProxy,
  BoardMemberResponse,
  BoardMemberResponsePaginatedResult
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { AlertComponent } from "../../shared/components/alert/alert.component";
import { AlertType } from '@core/enums/alert-type';

// Core interfaces and enums
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';

@Component({
  selector: 'app-members',
  standalone: true,
  imports: [CommonModule, TranslateModule, PageHeaderComponent, BreadcrumbComponent, MemberCardComponent, AlertComponent],
  templateUrl: './members.component.html',
  styleUrl: './members.component.scss',
})
export class MembersComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Permissions
  isHasPermissionAdd: boolean = false;
  isHasPermissionEdit: boolean = false;
  isHasPermissionDelete: boolean = false;

  // Data
  fundId: number = 0;
  currentFundName: string = '';
  members: BoardMemberResponse[] = [];
  filteredMembers: BoardMemberResponse[] = [];
  originalMembers: BoardMemberResponse[] = []; // Store original data for client-side search

  // State
  isLoading: boolean = false;
  hasError: boolean = false;
  errorMessage: string = '';
  searchTerm: string = '';

  // Pagination
  currentPage: number = 1;
  pageSize: number = 20;
  totalCount: number = 0;
  totalPages: number = 0;

  // Business Logic
  readonly MAX_MEMBERS = 15;
  AlertType = AlertType
  isDialogOpen = false;

  // UI state
  breadcrumbSizeEnum = SizeEnum;

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [
    // { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'sidebar.funds', url: '/admin/investment-funds' },
     {
        label: 'INVESTMENT_FUNDS.FORM.FUND_NAME',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`
      },
    // { label: 'BREADCRUMB.FUND_DETAILS', url: '' },
    { label: 'BREADCRUMB.MEMBERS', url: '', disabled: true }
  ];
  isFundManager: boolean = false;
  isLegalCouncil: boolean = false;
  isBoardSecretary: boolean = false;


  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private tokenService: TokenService,
    private dialog: MatDialog,
    private boardMembersService: BoardMembersServiceProxy,
    private errorModalService: ErrorModalService
  ) {}
  ngOnInit(): void {
    this.initializePermissions();
    this.initializeFundId();
    this.loadMembers();
    this.updateBreadcrumb();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializePermissions(): void {
    this.isHasPermissionAdd = this.tokenService.hasPermission('BoardMember.Create');
    this.isHasPermissionEdit = this.tokenService.hasPermission('BoardMember.Edit');
    this.isHasPermissionDelete = this.tokenService.hasPermission('BoardMember.Delete');

    this.isFundManager = this.tokenService.hasRole(userRole.fundManager);
    this.isLegalCouncil = this.tokenService.hasRole(userRole.legalCouncil);
    this.isBoardSecretary = this.tokenService.hasRole(userRole.boardSecretary);
  }

  private initializeFundId(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.fundId = +params['fundId'] || 0;
        if (this.fundId) {
          this.updateBreadcrumb();
          this.loadMembers();
        }
      });
  }

  loadMembers(): void {
    if (!this.fundId) {
      console.error('Fund ID is required to load members');
      return;
    }

    this.isLoading = true;
    this.hasError = false;

    this.boardMembersService.boardMembersList(
      this.fundId,
      this.currentPage - 1, // API uses 0-based indexing
      this.pageSize,
      '', // Remove search term for server-side, we'll do client-side filtering
      'lastUpdateDate desc' // Order by last update date DESC as per user story
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (response: BoardMemberResponsePaginatedResult) => {
        this.isLoading = false;
        if (response.successed) {
          if(response.data)
          {
          this.members = response.data;
          this.originalMembers = [...this.members]; // Store original data
          this.filteredMembers = [...this.members];
          this.totalCount = response.totalCount || 0;
          this.totalPages = response.totalPages || 0;

          // Apply current search filter if exists
          if (this.searchTerm) {
            this.applyClientSideSearch(this.searchTerm);
          }
          }
          else
          {
           this.hasError = false;
          this.handleLoadError(response.message || 'INVESTMENT_FUNDS.MEMBERS.NO_MEMBERS');
          }
        } else {
          this.hasError = true;
          this.handleLoadError(response.message || 'INVESTMENT_FUNDS.MEMBERS.NO_MEMBERS');
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.hasError = true;
        this.handleLoadError('INVESTMENT_FUNDS.MEMBERS.LOAD_ERROR');
        console.error('Error loading board members:', error);
      }
    });
  }

  private handleLoadError(message: string): void {
    this.errorMessage = message;
    this.members = [];
    this.originalMembers = [];
    this.filteredMembers = [];
    this.totalCount = 0;
  }

  get hasMembers(): boolean {
    return this.filteredMembers.length > 0;
  }

  get isMaxMembersReached(): boolean {
    return this.totalCount >= this.MAX_MEMBERS;
  }

  get hasChairman(): boolean {
    return this.members.some(member => member.isChairman && member.isActive);
  }

  addNewMember(): void {
    // This method should not be called when button is disabled, but add safety check
    if (this.isMaxMembersReached || this.isDialogOpen) {
      return;
    }



    this.isDialogOpen = true;

    if (this.isMaxMembersReached) {
      this.errorModalService.showError('INVESTMENT_FUNDS.MEMBERS.MAX_MEMBERS_REACHED');
      return;
    }

    const dialogRef = this.dialog.open(AddMemberComponent, {
      width: '600px',
      data: {
        fundId: this.fundId,
        hasChairman: this.hasChairman
      } as AddMemberDialogData
    });

    dialogRef.afterClosed().subscribe(result => {
      this.isDialogOpen = false;

      if (result) {
        // Refresh the members list if a member was successfully added
        this.loadMembers();
      }
    });
  }

  onEditMember(member: BoardMemberResponse): void {
    // TODO: Implement edit member functionality
    console.log('Edit member:', member);
  }

  onDeleteMember(member: BoardMemberResponse): void {
    // TODO: Implement delete member functionality
    console.log('Delete member:', member);
  }

  onSearch(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.applyClientSideSearch(searchTerm);
  }

  private applyClientSideSearch(searchTerm: string): void {
    const term = searchTerm.toLowerCase().trim();

    if (!this.originalMembers) {
      return;
    }

    if (!term) {
      // If search term is empty, show all members
      this.filteredMembers = [...this.originalMembers];
      this.members = [...this.originalMembers];
    } else {
      // Filter members based on member name and other searchable fields
      this.filteredMembers = this.originalMembers.filter((member) => {
        const memberName = member.memberName?.toLowerCase() || '';
        //const memberTypeDisplay = member.memberTypeDisplay?.toLowerCase() || '';
        //const statusDisplay = member.statusDisplay?.toLowerCase() || '';
        //const roleDisplay = member.roleDisplay?.toLowerCase() || '';

        return memberName.includes(term)
                //|| memberTypeDisplay.includes(term) ||
                //statusDisplay.includes(term) ||
                //roleDisplay.includes(term);
      });

      this.members = [...this.filteredMembers];
    }
  }

  openFilter(): void {
    // TODO: Implement filter functionality
    console.log('Open filter');
  }

  trackByMemberId(_index: number, member: BoardMemberResponse): number {
    return member.id;
  }

  private updateBreadcrumb(): void {
    // Update breadcrumb with fund name if available
    this.breadcrumbItems = [

     { label: 'sidebar.funds', url: '/admin/investment-funds' },
      { label: 'INVESTMENT_FUNDS.FORM.FUND_NAME', url: `/admin/investment-funds/fund-details?id=${this.fundId}` },
      { label: 'BREADCRUMB.MEMBERS', url: '', disabled: true }
    ];
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url && !item.disabled) {
      this.router.navigateByUrl(item.url);
    }
  }
}
