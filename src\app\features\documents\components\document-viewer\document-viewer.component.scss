.document-viewer-dialog {
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  max-height: 800px;
  display: flex;
  flex-direction: column;

  .viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e2e8f0;
    background-color: #f8f9fa;

    .document-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .file-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
        color: #4a5568;
      }

      .file-details {
        .file-name {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #1a365d;
        }

        .file-size {
          margin: 4px 0 0 0;
          font-size: 12px;
          color: #718096;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;

      .close-button {
        color: #718096;
        
        &:hover {
          color: #4a5568;
        }
      }
    }
  }

  .viewer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      gap: 16px;

      p {
        color: #718096;
        margin: 0;
      }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      padding: 48px;
      text-align: center;

      .error-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #e53e3e;
        margin-bottom: 16px;
      }

      h4 {
        margin: 0 0 8px 0;
        color: #4a5568;
      }

      p {
        margin: 0 0 16px 0;
        color: #718096;
      }

      .error-details {
        font-size: 14px;
        margin-bottom: 24px !important;
      }

      .error-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
        flex-wrap: wrap;
      }
    }

    .preview-container {
      flex: 1;
      display: flex;
      flex-direction: column;

      .document-preview {
        flex: 1;
        width: 100%;
        border: none;
      }

      .unsupported-preview {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px;
        text-align: center;

        .large-icon {
          font-size: 64px;
          width: 64px;
          height: 64px;
          color: #cbd5e0;
          margin-bottom: 24px;
        }

        h4 {
          margin: 0 0 8px 0;
          color: #4a5568;
        }

        p {
          margin: 0 0 24px 0;
          color: #718096;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .document-viewer-dialog {
    .viewer-header {
      .document-info {
        flex-direction: row-reverse;
      }

      .header-actions {
        flex-direction: row-reverse;
      }
    }
  }
}
